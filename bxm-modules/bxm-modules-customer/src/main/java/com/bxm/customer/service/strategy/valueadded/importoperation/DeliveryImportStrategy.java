package com.bxm.customer.service.strategy.valueadded.importoperation;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.valueAdded.*;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.helper.ValueAddedImportValidationHelper;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 交付导入策略实现
 *
 * 处理交付操作的导入逻辑：
 * 1. 验证交付单状态必须为"已提交待交付"
 * 2. 修改状态为"已交付待确认"
 * 3. 保存附件文件到value_added_file表
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class DeliveryImportStrategy implements ImportOperationStrategy {

    @Autowired
    private IValueAddedDeliveryOrderService deliveryOrderService;

    @Autowired
    private IValueAddedFileService fileService;

    @Autowired
    private IValueAddedItemTypeService itemTypeService;

    @Autowired
    private ValueAddedImportValidationHelper valueAddedImportValidationHelper;

    @Override
    public ValueAddedBatchImportOperationType getSupportedOperationType() {
        return ValueAddedBatchImportOperationType.DELIVERY;
    }



    @Override
    public void validateOrderStatus(ValueAddedDeliveryOrder order) {
        String currentStatus = order.getStatus();
        if (!ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(currentStatus)) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status does not allow delivery operation, current status: %s, required status: %s",
                            order.getDeliveryOrderNo(),
                            ValueAddedDeliveryOrderStatus.getByCode(currentStatus).getDescription(),
                            ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getDescription())
            );
        }
    }

    @Override
    public String getTargetStatus(String currentStatus) {
        // 交付操作：已提交待交付 -> 已交付待确认
        if (ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(currentStatus)) {
            return ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode();
        }
        return null;
    }



    @Override
    public int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationDTO importDTO) {

        int savedCount = 0;
        String deliveryOrderNo = order.getDeliveryOrderNo();

        for (Map.Entry<String, String> entry : extractedFiles.entrySet()) {
            try {
                String fileName = entry.getKey();
                String filePath = entry.getValue();

                // Check if file name is related to delivery order number
                if (isFileRelatedToOrder(fileName, deliveryOrderNo)) {
                    ValueAddedFile file = new ValueAddedFile();
                    file.setDeliveryOrderNo(deliveryOrderNo);
                    file.setFileName(fileName);
                    file.setFileUrl(filePath);
                    file.setFileType(1); // 1-交付材料附件
                    file.setStatus(1); // 1-处理完成
                    file.setIsDel(false);
                    file.setRemark("批量交付导入");
                    file.setCreateBy(SecurityUtils.getUserId().toString());

                    boolean saved = fileService.save(file);
                    if (saved) {
                        savedCount++;
                        log.debug("File saved successfully: {} -> {}", fileName, deliveryOrderNo);
                    }
                }
            } catch (Exception e) {
                log.warn("File save failed: {}, error: {}", entry.getKey(), e.getMessage());
            }
        }

        return savedCount;
    }

    /**
     * 判断文件是否与交付单相关
     * 根据文件名包含交付单编号或其他规则判断
     */
    private boolean isFileRelatedToOrder(String fileName, String deliveryOrderNo) {
        if (fileName == null || deliveryOrderNo == null) {
            return false;
        }

        // 简单的匹配规则：文件名包含交付单编号
        return fileName.toUpperCase().contains(deliveryOrderNo.toUpperCase());
    }

    @Override
    public TemplateParseResult parseTemplateFile(InputStream templateStream, String fileName) throws Exception {
        log.info("开始解析交付操作Excel模板文件: {}", fileName);

        try {
            // 使用ExcelUtil解析Excel文件为DeliveryImportExcelDTO列表
            ExcelUtil<DeliveryImportExcelDTO> excelUtil = new ExcelUtil<>(DeliveryImportExcelDTO.class);
            List<DeliveryImportExcelDTO> dataList = excelUtil.importExcel(templateStream);

            // 设置行号用于错误定位
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setRowNumber(i + 2); // Excel从第2行开始是数据行
            }

            // 使用新的基础校验逻辑
            ImportValidationResult basicValidationResult = valueAddedImportValidationHelper.performBasicValidation(dataList);

            if (!basicValidationResult.getIsValid()) {
                // 基础校验失败，返回错误结果
                return TemplateParseResult.failure(basicValidationResult.getErrors());
            }

            // 基础校验通过，进行交付操作特有的校验
            return validateDeliverySpecificData(basicValidationResult.getValidData());

        } catch (Exception e) {
            log.error("交付操作Excel模板文件解析失败: {}", e.getMessage(), e);
            throw new Exception("交付操作Excel模板文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验交付操作特有数据
     */
    private TemplateParseResult validateDeliverySpecificData(List<? extends BaseImportExcelDTO> dataList) {
        List<DeliveryImportExcelDTO> validData = new ArrayList<>();
        List<ImportValidationErrorDTO> errors = new ArrayList<>();

        for (BaseImportExcelDTO baseData : dataList) {
            if (!(baseData instanceof DeliveryImportExcelDTO)) {
                continue;
            }

            DeliveryImportExcelDTO data = (DeliveryImportExcelDTO) baseData;
            List<String> rowErrors = new ArrayList<>();

            // 校验1：备注和附件至少要其一
            if (StringUtils.isEmpty(data.getRemark()) && StringUtils.isEmpty(data.getAttachmentFileName())) {
                rowErrors.add("备注和附件文件名至少需要填写其中一项");
            }

            if (rowErrors.isEmpty()) {
                validData.add(data);
            } else {
                // 为每个错误创建一个错误记录
                for (String error : rowErrors) {
                    errors.add(ImportValidationErrorDTO.create(
                            data.getRowNumber(),
                            data.getDeliveryOrderNo(),
                            data.getCustomerName(),
                            data.getCreditCode(),
                            error,
                            "交付特有校验错误"
                    ));
                }
            }
        }

        // 如果有错误，生成批次号
        if (!errors.isEmpty()) {
            String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            return TemplateParseResult.withErrors(validData, errors, batchNo);
        }

        return TemplateParseResult.success(validData);
    }


}
